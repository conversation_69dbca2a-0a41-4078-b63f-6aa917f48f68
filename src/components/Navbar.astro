---
import { getCompleteMenu } from '../utils/menuParser.ts';

// Get the menu data to extract categories
const menuData = getCompleteMenu();

// Extract categories from the menu data
const categories = menuData.sections
  .filter(section => section.type === 'category')
  .map(section => ({
    name: section.categoryName || '',
    slug: (section.categoryName || '').toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-'),
    products: section.products?.map(product => {
      const title = product.content.match(/^# (.+)$/m)?.[1] || 'Untitled Product';
      const slug = title.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-');
      return { title, slug };
    }) || []
  }));

// Function to create a URL-friendly slug from a title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}
---

<nav class="navbar">
  <div class="navbar-container">
    <!-- Logo/Brand -->
    <div class="navbar-brand">
      <a href="#top" class="brand-link">
        <img src="https://support.discord.com/hc/user_images/oPr9UYgjBa88_plaktvSww.png" width="40" alt="SlugRC" />
        <span>SlugRC</span>
      </a>
    </div>

    <!-- Desktop Navigation -->
    <div class="navbar-menu">
      {categories.map((category) => (
        <div class="navbar-item dropdown">
          <a href={`#${category.slug}`} class="navbar-link">
            {category.name}
          </a>
          {category.products.length > 0 && (
            <div class="dropdown-menu">
              {category.products.map((product) => (
                <a href={`#${product.slug}`} class="dropdown-item">
                  {product.title}
                </a>
              ))}
            </div>
          )}
        </div>
      ))}
      <!-- Theme toggle as last navbar item -->
      <div class="navbar-item">
        <button id="navbar-theme-toggle" class="navbar-theme-toggle" aria-label="Toggle dark mode">
          <span class="theme-icon light-icon">☀️</span>
          <span class="theme-icon dark-icon">🌙</span>
        </button>
      </div>
    </div>

    <!-- Mobile Menu Button -->
    <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
    </button>
  </div>

  <!-- Mobile Menu Overlay -->
  <div class="mobile-menu-overlay">
    <div class="mobile-menu">
      <div class="mobile-menu-header">
        <span class="mobile-menu-title">Menu</span>
        <div class="mobile-menu-controls">
          <button id="mobile-theme-toggle" class="mobile-theme-toggle" aria-label="Toggle dark mode">
            <span class="theme-icon light-icon">☀️</span>
            <span class="theme-icon dark-icon">🌙</span>
          </button>
          <button class="mobile-menu-close" aria-label="Close mobile menu">×</button>
        </div>
      </div>
      <div class="mobile-menu-content">
        {categories.map((category) => (
          <div class="mobile-category">
            <button class="mobile-category-toggle" data-category={category.slug}>
              {category.name}
              <span class="mobile-category-arrow">▼</span>
            </button>
            {category.products.length > 0 && (
              <div class="mobile-category-products" data-category-content={category.slug}>
                {category.products.map((product) => (
                  <a href={`#${product.slug}`} class="mobile-product-link">
                    {product.title}
                  </a>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  </div>
</nav>

<style>
  .navbar {
    position: fixed;
    top: -80px; /* Start hidden above viewport */
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    box-shadow: 0 2px 8px var(--shadow-light);
    z-index: 1000;
    transition: all 0.3s ease;
  }

  .navbar.visible {
    top: 0; /* Show when visible class is added */
  }

  .navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    height: 60px;
  }

  .navbar-brand {
    display: flex;
    align-items: center;
  }

  .brand-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.1rem;
  }

  .brand-link img {
    border-radius: 4px;
  }

  /* Desktop Navigation */
  .navbar-menu {
    display: flex;
    align-items: center;
    gap: 0;
  }

  .navbar-item {
    position: relative;
  }

  .navbar-link {
    display: block;
    padding: 1rem 1.25rem;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
    white-space: nowrap;
  }

  .navbar-link:hover {
    color: var(--accent-primary);
  }

  /* Navbar theme toggle */
  .navbar-theme-toggle {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: 50%;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0 0.5rem;
  }

  .navbar-theme-toggle:hover {
    background: var(--bg-tertiary);
    transform: scale(1.1);
  }

  .navbar-theme-toggle .theme-icon {
    font-size: 1rem;
    position: absolute;
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  .navbar-theme-toggle .light-icon {
    opacity: 1;
    transform: rotate(0deg);
  }

  .navbar-theme-toggle .dark-icon {
    opacity: 0;
    transform: rotate(180deg);
  }

  :root[data-theme="dark"] .navbar-theme-toggle .light-icon {
    opacity: 0;
    transform: rotate(180deg);
  }

  :root[data-theme="dark"] .navbar-theme-toggle .dark-icon {
    opacity: 1;
    transform: rotate(0deg);
  }

  /* Dropdown Menu */
  .dropdown {
    position: relative;
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow-medium);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1001;
  }

  .dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid var(--border-light);
  }

  .dropdown-item:last-child {
    border-bottom: none;
  }

  .dropdown-item:hover {
    background: var(--bg-secondary);
    color: var(--accent-primary);
  }

  /* Mobile Menu Button */
  .mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
  }

  .hamburger-line {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    margin: 2px 0;
    transition: all 0.3s ease;
  }

  .mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
  }

  .mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  /* Mobile Menu Overlay */
  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
  }

  .mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
  }

  .mobile-menu {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100vh;
    background: var(--bg-primary);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
  }

  .mobile-menu-overlay.active .mobile-menu {
    transform: translateX(0);
  }

  .mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-light);
  }

  .mobile-menu-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .mobile-menu-title {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-primary);
  }

  .mobile-theme-toggle {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: 50%;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .mobile-theme-toggle .theme-icon {
    font-size: 0.9rem;
    position: absolute;
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  .mobile-theme-toggle .light-icon {
    opacity: 1;
    transform: rotate(0deg);
  }

  .mobile-theme-toggle .dark-icon {
    opacity: 0;
    transform: rotate(180deg);
  }

  :root[data-theme="dark"] .mobile-theme-toggle .light-icon {
    opacity: 0;
    transform: rotate(180deg);
  }

  :root[data-theme="dark"] .mobile-theme-toggle .dark-icon {
    opacity: 1;
    transform: rotate(0deg);
  }

  .mobile-menu-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-primary);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mobile-menu-content {
    padding: 1rem 0;
  }

  .mobile-category {
    border-bottom: 1px solid var(--border-light);
  }

  .mobile-category:last-child {
    border-bottom: none;
  }

  .mobile-category-toggle {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: none;
    border: none;
    text-align: left;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
  }

  .mobile-category-link {
    display: block;
    padding: 1rem;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
  }

  .mobile-category-arrow {
    transition: transform 0.2s ease;
  }

  .mobile-category-toggle.active .mobile-category-arrow {
    transform: rotate(180deg);
  }

  .mobile-category-products {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: var(--bg-secondary);
  }

  .mobile-category-products.active {
    max-height: 500px;
  }

  .mobile-product-link {
    display: block;
    padding: 0.75rem 2rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-bottom: 1px solid var(--border-light);
  }

  .mobile-product-link:last-child {
    border-bottom: none;
  }

  .mobile-product-link:hover {
    background: var(--bg-tertiary);
    color: var(--accent-primary);
  }

  /* Mobile Styles */
  @media (max-width: 768px) {
    .navbar-menu {
      display: none;
    }

    .mobile-menu-toggle {
      display: flex;
    }
  }

  /* Desktop Styles */
  @media (min-width: 769px) {
    .mobile-menu-overlay {
      display: none;
    }
  }
</style>

<script>
  // Mobile menu functionality
  document.addEventListener('DOMContentLoaded', () => {
    const navbar = document.querySelector('.navbar');

    // Scroll detection to show/hide navbar
    function handleScroll() {
      const tocSection = document.querySelector('.toc-section');
      const themeToggleContainer = document.querySelector('.theme-toggle-container');

      if (tocSection && navbar) {
        const tocBottom = tocSection.offsetTop + tocSection.offsetHeight;
        const scrollPosition = window.scrollY;

        if (scrollPosition > tocBottom) {
          navbar.classList.add('visible');
          // Hide main theme toggle when navbar is visible
          if (themeToggleContainer) {
            themeToggleContainer.style.display = 'none';
          }
        } else {
          navbar.classList.remove('visible');
          // Show main theme toggle when navbar is hidden
          if (themeToggleContainer) {
            themeToggleContainer.style.display = 'block';
          }
        }
      }
    }

    // Add scroll listener
    window.addEventListener('scroll', handleScroll);

    // Initial check
    handleScroll();

    // Theme toggle functionality
    function toggleTheme() {
      const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
      const newTheme = currentTheme === 'light' ? 'dark' : 'light';

      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);

      // Update all toggles
      updateToggleButton(newTheme);
    }

    // Mobile theme toggle functionality
    const mobileThemeToggle = document.querySelector('#mobile-theme-toggle');
    if (mobileThemeToggle) {
      mobileThemeToggle.addEventListener('click', toggleTheme);
    }

    // Navbar theme toggle functionality
    const navbarThemeToggle = document.querySelector('#navbar-theme-toggle');
    if (navbarThemeToggle) {
      navbarThemeToggle.addEventListener('click', toggleTheme);
    }

    function updateToggleButton(theme) {
      const ariaLabel = theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode';

      // Update main theme toggle
      const toggle = document.getElementById('theme-toggle');
      if (toggle) {
        toggle.setAttribute('aria-label', ariaLabel);
      }

      // Update mobile theme toggle
      const mobileToggle = document.getElementById('mobile-theme-toggle');
      if (mobileToggle) {
        mobileToggle.setAttribute('aria-label', ariaLabel);
      }

      // Update navbar theme toggle
      const navbarToggle = document.getElementById('navbar-theme-toggle');
      if (navbarToggle) {
        navbarToggle.setAttribute('aria-label', ariaLabel);
      }
    }

    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mobileOverlay = document.querySelector('.mobile-menu-overlay');
    const mobileClose = document.querySelector('.mobile-menu-close');
    const categoryToggles = document.querySelectorAll('.mobile-category-toggle');

    // Toggle mobile menu
    function toggleMobileMenu() {
      mobileToggle?.classList.toggle('active');
      mobileOverlay?.classList.toggle('active');
      document.body.style.overflow = mobileOverlay?.classList.contains('active') ? 'hidden' : '';
    }

    // Close mobile menu
    function closeMobileMenu() {
      mobileToggle?.classList.remove('active');
      mobileOverlay?.classList.remove('active');
      document.body.style.overflow = '';
    }

    // Event listeners
    mobileToggle?.addEventListener('click', toggleMobileMenu);
    mobileClose?.addEventListener('click', closeMobileMenu);
    mobileOverlay?.addEventListener('click', (e) => {
      if (e.target === mobileOverlay) {
        closeMobileMenu();
      }
    });

    // Category toggles in mobile menu
    categoryToggles.forEach(toggle => {
      toggle.addEventListener('click', () => {
        const category = toggle.dataset.category;
        const content = document.querySelector(`[data-category-content="${category}"]`);
        
        toggle.classList.toggle('active');
        content?.classList.toggle('active');
      });
    });

    // Close mobile menu when clicking on links
    document.querySelectorAll('.mobile-product-link, .mobile-category-link').forEach(link => {
      link.addEventListener('click', closeMobileMenu);
    });

    // Smooth scrolling for navbar links
    document.querySelectorAll('.navbar-link, .dropdown-item, .mobile-product-link, .mobile-category-link').forEach(link => {
      link.addEventListener('click', (e) => {
        const href = link.getAttribute('href');
        if (href?.startsWith('#')) {
          e.preventDefault();
          const target = document.querySelector(href);
          if (target) {
            const navbarHeight = document.querySelector('.navbar')?.offsetHeight || 60;
            const targetPosition = target.offsetTop - navbarHeight - 20;
            
            window.scrollTo({
              top: targetPosition,
              behavior: 'smooth'
            });
          }
        }
      });
    });
  });
</script>
